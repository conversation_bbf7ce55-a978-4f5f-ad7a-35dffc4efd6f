import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_it/get_it.dart';
import 'package:note_x/lib.dart';

/// A helper class for managing folder movement operations
class MoveToFolderHelper {
  /// Shows the move to folder bottom sheet
  static void showBottomSheet(
    BuildContext context, {
    List<String>? foldersToBeMovedIds,
    String? jumpToFolderId,
    List<NoteModel>? notesToBeMoved,
    VoidCallback? onFolderMoved,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: context.colorScheme.mainNeutral,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => _MoveToFolderBottomSheet(
        foldersToBeMovedIds: foldersToBeMovedIds,
        jumpToFolderId: jumpToFolderId,
        notesToBeMoved: notesToBeMoved,
        onFolderMoved: onFolderMoved,
      ),
    );
  }
}

/// The bottom sheet widget for moving folders
class _MoveToFolderBottomSheet extends StatelessWidget {
  final List<String>? foldersToBeMovedIds;
  final String? jumpToFolderId;
  final List<NoteModel>? notesToBeMoved;
  final VoidCallback? onFolderMoved;

  const _MoveToFolderBottomSheet({
    Key? key,
    this.foldersToBeMovedIds,
    this.jumpToFolderId,
    this.notesToBeMoved,
    this.onFolderMoved,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<MoveFolderCubit>(
          create: (context)  {
           final cubit = GetIt.instance.get<MoveFolderCubit>();
           cubit.initialize(jumpToFolderId: jumpToFolderId);
           cubit.appCubit = GetIt.instance.get<AppCubit>();
           return cubit;
          },
        ),
      ],
      child: BlocConsumer<MoveFolderCubit, MoveFolderState>(
        listener: _handleStateChanges,
        builder: (context, state) => _buildContent(context, state),
      ),
    );
  }

  void _handleStateChanges(BuildContext context, MoveFolderState state) {
    if (state.oneShotEvent == MoveFolderOneShotEvent.folderMoved) {
      Navigator.pop(context);
      CommonDialogs.showToast(
        'Folder moved successfully',
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_SHORT,
      );
      onFolderMoved?.call();
    } else if (state.oneShotEvent == MoveFolderOneShotEvent.error) {
      _handleError(context, state.errorMessage);
    }
  }

  void _handleError(BuildContext context, String? errorMessage) {
    debugPrint('MoveFolderCubit error: $errorMessage');
    String message = errorMessage ?? 'Unknown error';

    if (message.contains('Cannot move folder into itself')) {
      message = 'Cannot move folder into itself';
    } else if (message.contains('Cannot move folder into its own subfolder')) {
      message = 'Cannot move folder into its own subfolder';
    } else if (message.contains('404')) {
      message = 'Folder not found or operation not allowed';
    } else if (message.contains('Failed to move folder')) {
      message = 'Failed to move folder. Please try again.';
    }

    CommonDialogs.showToast(
      message,
      gravity: ToastGravity.BOTTOM,
      length: Toast.LENGTH_LONG,
    );
  }

  Widget _buildContent(BuildContext context, MoveFolderState state) {
    if (state.isLoading) {
      return Center(
        child: CupertinoActivityIndicator(radius: 16.r),
      );
    }

    return StatefulBuilder(
      builder: (context, setState) => SafeArea(
        child: Container(
          padding: EdgeInsets.only(
            left: context.isTablet ? 16 : 16.w,
            right: context.isTablet ? 16 : 16.w,
            top: context.isTablet ? 16 : 16.h,
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          height: MediaQuery.of(context).size.height * 0.95,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(context),
              Expanded(child: _buildFolderList(context, state)),
              _buildActionButtons(context, state),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: CommonText(
            S.current.add_folder,
            style: TextStyle(
              fontSize: context.isTablet ? 22 : 20.sp,
              fontWeight: FontWeight.w600,
              color: context.colorScheme.mainPrimary,
            ),
          ),
        ),
        GestureDetector(
          onTap: () => Navigator.pop(context),
          child: SvgPicture.asset(
            Assets.icons.icCloseWhite,
            width: context.isTablet ? 32 : 24.w,
            height: context.isTablet ? 32 : 24.w,
            fit: BoxFit.contain,
            colorFilter: ColorFilter.mode(
              context.colorScheme.mainGray,
              BlendMode.srcIn,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFolderList(BuildContext context, MoveFolderState state) {
    return ListView(
      shrinkWrap: true,
      children: state.allFolderViewData
          .map((folder) => MoveFolderTile(
                folder: folder,
                foldersToBeMovedIds: foldersToBeMovedIds,
                onTap: (folder) => _handleFolderTap(context, folder),
                onToggleExpansion: (folderId) {
                  context
                      .read<MoveFolderCubit>()
                      .toggleFolderExpansion(folderId);
                },
              ))
          .toList(),
    );
  }

  void _handleFolderTap(BuildContext context, MoveFolderViewData folder) {
    if (folder.isInvalid) {
      CommonDialogs.showToast(
        'Cannot move folder into itself or its subfolder',
        gravity: ToastGravity.BOTTOM,
        length: Toast.LENGTH_SHORT,
      );
    } else {
      context.read<MoveFolderCubit>().selectFolder(folder);
    }
  }

  Widget _buildActionButtons(BuildContext context, MoveFolderState state) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: Row(
        children: [
          Expanded(
            child: _buildCreateButton(context, state),
          ),
          AppConstants.kSpacingItemW10,
          Expanded(
            child: _buildMoveButton(context, state),
          ),
        ],
      ),
    );
  }

  Widget _buildCreateButton(BuildContext context, MoveFolderState state) {
    return AppCommonButton(
      backgroundColor: context.colorScheme.mainSecondary,
      borderRadius: BorderRadius.circular(24.r),
      height: context.isTablet ? 44 : 44.h,
      onPressed: state.selectedFolder == null
          ? null
          : () =>
              _showCreateFolderDialog(context, state.selectedFolder!.backendId),
      textWidget: CommonText(
        S.current.create,
        style: TextStyle(
          fontSize: context.isTablet ? 18 : 16.sp,
          color: context.colorScheme.mainPrimary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildMoveButton(BuildContext context, MoveFolderState state) {
    return AppCommonButton(
      backgroundColor: context.colorScheme.mainBlue,
      borderRadius: BorderRadius.circular(24.r),
      height: context.isTablet ? 44 : 44.h,
      onPressed: state.selectedFolder == null
          ? null
          : () => _handleMoveAction(context, state),
      textWidget: CommonText(
        S.current.move,
        style: TextStyle(
          fontSize: context.isTablet ? 18 : 16.sp,
          color: context.colorScheme.mainPrimary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _showCreateFolderDialog(BuildContext context, String selectedFolderId) {
    final controller = TextEditingController();
    showCreateFolderDialog(
      context,
      controller: controller,
      onPressed: () async {
        try {
          await context.read<MoveFolderCubit>().createSubfolder(
                name: controller.text.trim(),
                parentFolderId: selectedFolderId,
              );
          if (context.mounted) {
            Navigator.pop(context); // Only close the create folder dialog
            // Bottom sheet stays open to show the updated folder list
          }
        } catch (e) {
          debugPrint('Error creating subfolder: $e');
          if (context.mounted) {
            Navigator.pop(context); // Close dialog on error
          }
        }
      },
      onClosed: () {
        controller.dispose();
      },
      title: S.current.create_new_folder,
      contentButton: S.current.create,
      hintText: S.current.required,
      initialValue: '',
    );
  }

  Future<void> _handleMoveAction(
      BuildContext context, MoveFolderState state) async {
    await context.read<MoveFolderCubit>().moveFoldersAndNotes(
          folderIds: foldersToBeMovedIds ?? [],
          notes: notesToBeMoved ?? [],
          targetFolderId: state.selectedFolder!.backendId,
        );
  }
}

/// A widget representing a folder tile in the move folder list
class MoveFolderTile extends StatelessWidget {
  final MoveFolderViewData folder;
  final Function(MoveFolderViewData folder)? onTap;
  final Function(String folderId)? onToggleExpansion;
  final List<String>? foldersToBeMovedIds;
  final int level;

  const MoveFolderTile({
    Key? key,
    required this.folder,
    this.onTap,
    this.onToggleExpansion,
    this.foldersToBeMovedIds,
    this.level = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MoveFolderCubit, MoveFolderState>(
      builder: (context, state) {
        final isSelected = state.selectedFolder?.backendId == folder.backendId;

        return Column(
          children: [
            ListTile(
              contentPadding: EdgeInsets.only(left: 16.w * level),
              tileColor: isSelected
                  ? context.colorScheme.mainSecondary
                  : folder.isInvalid
                      ? context.colorScheme.mainNeutral
                      : null,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24.r),
              ),
              leading: _buildLeading(context, folder.isInvalid),
              title: Text(
                folder.folderName,
                style: TextStyle(
                  color: folder.isInvalid ? Colors.white.withOpacity(0.24) : null,
                ),
              ),
              onTap: () => onTap?.call(folder),
            ),
            if (folder.isExpanded)
              ...folder.subfolders.map((subfolder) => MoveFolderTile(
                    folder: subfolder,
                    onTap: onTap,
                    onToggleExpansion: onToggleExpansion,
                    foldersToBeMovedIds: foldersToBeMovedIds,
                    level: level + 1,
                  )),
          ],
        );
      },
    );
  }

  Widget _buildLeading(BuildContext context, bool isInvalid) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (folder.subfolders.isNotEmpty)
          GestureDetector(
            onTap: () => onToggleExpansion?.call(folder.backendId),
            child: SvgPicture.asset(
              folder.isExpanded
                  ? Assets.icons.icExpandMore
                  : Assets.icons.icExpandLess,
              width: context.isTablet ? 24 : 16.w,
              height: context.isTablet ? 24 : 16.w,
              fit: BoxFit.contain,
              colorFilter: isInvalid
                  ? ColorFilter.mode(
                      Colors.white.withOpacity(0.24),
                      BlendMode.srcIn,
                    )
                  : null,
            ),
          ),
        SvgPicture.asset(
          Assets.icons.icFlipFolderMini,
          width: context.isTablet ? 48 : 32.w,
          height: context.isTablet ? 48 : 32.w,
          fit: BoxFit.contain,
          colorFilter: isInvalid
              ? ColorFilter.mode(
                  Colors.white.withOpacity(0.24),
                  BlendMode.srcIn,
                )
              : null,
        ),
      ],
    );
  }
}
