import 'package:note_x/lib.dart';

class MoveFolderCubit extends BaseCubit<MoveFolderState> {
  MoveFolderCubit(super.initialState);

  final FolderService _folderService = FolderService();
  String? _currentJumpToFolderId;
  List<String>? _foldersToBeMovedIds;

  /// Initialize and load folder hierarchy
  Future<void> initialize({
    String? jumpToFolderId,
    List<String>? foldersToBeMovedIds,
  }) async {
    _currentJumpToFolderId = jumpToFolderId;
    _foldersToBeMovedIds = foldersToBeMovedIds;

    _emitLoading();

    try {
      final allFolders = HiveFolderService.getAllFolders();

      // Use simple map approach but filter for root folders only
      final rootFolders = allFolders
          .where((folder) => folder.parentFolderId == null || folder.parentFolderId!.isEmpty)
          .map((folder) => MoveFolderViewData.fromFolderModel(
            folder,
            jumpToFolderId: jumpToFolderId ?? '',
            foldersToBeMovedIds: foldersToBeMovedIds,
          ))
          .toList();

      final allFolderViewData = rootFolders;

      final selectedFolder = _findSelectedFolder(allFolderViewData, jumpToFolderId);

      _emitSuccess(allFolderViewData, selectedFolder);
    } catch (e) {
      _emitError(e.toString());
    }
  }

  /// Select a folder
  void selectFolder(MoveFolderViewData folder) {
    emit(state.copyWith(selectedFolder: folder));
  }

  /// Toggle folder expansion
  void toggleFolderExpansion(String folderId) {
    final updatedRootFolders =
        _toggleExpansionInList(state.allFolderViewData, folderId);
    emit(state.copyWith(allFolderViewData: updatedRootFolders));
  }

  /// Create subfolder
  Future<void> createSubfolder({
    required String name,
    required String parentFolderId,
  }) async {
    runCubitCatching(
        handleLoading: false,
        action: () async {

      _emitLoading();

      final newFolder = await _folderService.createSubfolder(
        name: name,
        parentFolderId: parentFolderId,
      );

      // Rebuild hierarchy with fresh data from Hive (folder is already saved)
      final allFolders = HiveFolderService.getAllFolders();

      final rootFolders = allFolders
          .where((folder) => folder.parentFolderId == null || folder.parentFolderId!.isEmpty)
          .map((folder) => MoveFolderViewData.fromFolderModel(
            folder,
            jumpToFolderId: _currentJumpToFolderId ?? '',
            foldersToBeMovedIds: _foldersToBeMovedIds,
          ))
          .toList();

      final allFolderViewData = rootFolders;

      // Ensure parent folder is expanded first
      final updatedRootFolders =
      _ensureParentExpanded(allFolderViewData, parentFolderId);

      // Find and select the newly created folder in the updated hierarchy
      final newFolderId = newFolder.backendId;
      MoveFolderViewData? newlyCreatedFolder;
      if (newFolderId.isNotEmpty) {
        newlyCreatedFolder = _findFolderById(updatedRootFolders, newFolderId);
      }

      emit(state.copyWith(
        oneShotEvent: MoveFolderOneShotEvent.success,
        allFolderViewData: updatedRootFolders,
        selectedFolder: newlyCreatedFolder,
        isLoading: false,
      ));
    });
  }

  /// Move folders and notes to target folder
  Future<void> moveFoldersAndNotes({
    required List<String> folderIds,
    required List<NoteModel> notes,
    required String targetFolderId,
  }) async {
    runCubitCatching(
      handleLoading: true,
        action: () async {
      // _emitLoading();

      _validateMoveOperation(folderIds, targetFolderId);

      await _folderService.bulkMoveNotesToFolder(
        targetFolderId: targetFolderId,
        folderIds: folderIds,
        noteIds: notes.map((note) => note.backendNoteId).toList(),
      );

      await initialize(
        jumpToFolderId: _currentJumpToFolderId,
        foldersToBeMovedIds: _foldersToBeMovedIds,
      );

      emit(state.copyWith(
        oneShotEvent: MoveFolderOneShotEvent.folderMoved,
        isLoading: false,
      ));
    });
  }

  // Private helper methods
  void _emitLoading() {
    emit(state.copyWith(
      oneShotEvent: MoveFolderOneShotEvent.loading,
      isLoading: true,
    ));
  }

  void _emitSuccess(List<MoveFolderViewData> allFolderViewData,
      MoveFolderViewData? selectedFolder) {
    emit(state.copyWith(
      oneShotEvent: MoveFolderOneShotEvent.success,
      allFolderViewData: allFolderViewData,
      selectedFolder: selectedFolder,
      isLoading: false,
    ));
  }

  void _emitError(String errorMessage) {
    emit(state.copyWith(
      oneShotEvent: MoveFolderOneShotEvent.error,
      errorMessage: errorMessage,
      isLoading: false,
    ));
  }

  MoveFolderViewData? _findSelectedFolder(
      List<MoveFolderViewData> rootFolders, String? jumpToFolderId) {
    if (jumpToFolderId == null || jumpToFolderId.isEmpty) return null;
    return _findFolderById(rootFolders, jumpToFolderId);
  }

  void _validateMoveOperation(List<String> folderIds, String targetFolderId) {
    for (final folderId in folderIds) {
      if (folderId == targetFolderId) {
        throw Exception('Cannot move folder into itself');
      }
      if (_isTargetDescendantOfFolder(folderId, targetFolderId)) {
        throw Exception('Cannot move folder into its own subfolder');
      }
    }
  }

  /// Build folder hierarchy from flat list
  List<MoveFolderViewData> _buildFolderHierarchy(
    List<FolderModel> allFolders,
    String? jumpToFolderId,
  ) {
    final folderMap = _createFolderMap(allFolders);
    final rootFolders =
        _buildRootFolders(allFolders, folderMap, jumpToFolderId);
    return _sortFoldersByDate(rootFolders);
  }

  Map<String, FolderModel> _createFolderMap(List<FolderModel> allFolders) {
    final folderMap = <String, FolderModel>{};
    for (final folder in allFolders) {
      folderMap[folder.backendId] = folder;
    }
    return folderMap;
  }

  List<MoveFolderViewData> _buildRootFolders(
    List<FolderModel> allFolders,
    Map<String, FolderModel> folderMap,
    String? jumpToFolderId,
  ) {
    final rootFolders = <MoveFolderViewData>[];

    for (final folder in allFolders) {
      if (_isRootFolder(folder, folderMap)) {
        final viewModel =
            _buildFolderViewModel(folder, folderMap, jumpToFolderId);
        rootFolders.add(viewModel);
      }
    }

    return rootFolders;
  }

  bool _isRootFolder(FolderModel folder, Map<String, FolderModel> folderMap) {
    return folder.parentFolderId == null ||
        folder.parentFolderId!.isEmpty ||
        !folderMap.containsKey(folder.parentFolderId!);
  }

  List<MoveFolderViewData> _sortFoldersByDate(
      List<MoveFolderViewData> folders) {
    folders.sort((a, b) => MyUtils.convertToTimestamp(b.createdAt)
        .compareTo(MyUtils.convertToTimestamp(a.createdAt)));
    return folders;
  }

  /// Build view model for a folder and its subfolders recursively
  MoveFolderViewData _buildFolderViewModel(
    FolderModel folder,
    Map<String, FolderModel> folderMap,
    String? jumpToFolderId,
  ) {
    // Find direct child folders from the folderMap instead of relying on folder.subfolders
    final childFolders = folderMap.values
        .where((f) => f.parentFolderId == folder.backendId)
        .toList();

    final subfolders = childFolders.map((subfolder) {
      return _buildFolderViewModel(subfolder, folderMap, jumpToFolderId);
    }).toList();

    final shouldExpand =
        _shouldExpandFolder(folder.backendId, jumpToFolderId, folderMap);

    return MoveFolderViewData.fromFolderModel(
      folder,
      isExpanded: shouldExpand,
      jumpToFolderId: jumpToFolderId ?? '',
      foldersToBeMovedIds: _foldersToBeMovedIds,
    ).copyWith(subfolders: subfolders);
  }

  /// Check if folder should be expanded based on jumpToFolderId
  bool _shouldExpandFolder(
    String folderBackendId,
    String? jumpToFolderId,
    Map<String, FolderModel> folderMap,
  ) {
    if (jumpToFolderId == null || jumpToFolderId.isEmpty) return false;

    // Expand if this is the target folder or contains the target folder
    return folderBackendId == jumpToFolderId ||
        _containsTargetFolder(folderBackendId, jumpToFolderId, folderMap);
  }

  /// Check if a folder contains the target folder in its hierarchy
  bool _containsTargetFolder(
    String folderBackendId,
    String targetFolderId,
    Map<String, FolderModel> folderMap,
  ) {
    final targetFolder = folderMap[targetFolderId];
    if (targetFolder == null) return false;

    String? currentParentId = targetFolder.parentFolderId;
    while (currentParentId != null && currentParentId.isNotEmpty) {
      if (currentParentId == folderBackendId) return true;

      final parentFolder = folderMap[currentParentId];
      currentParentId = parentFolder?.parentFolderId;
    }

    return false;
  }

  /// Find folder by ID in the hierarchy
  MoveFolderViewData? _findFolderById(
    List<MoveFolderViewData> folders,
    String folderId,
  ) {
    for (final folder in folders) {
      if (folder.backendId == folderId) return folder;

      final found = _findFolderById(folder.subfolders, folderId);
      if (found != null) return found;
    }
    return null;
  }

  /// Toggle expansion in folder list recursively
  List<MoveFolderViewData> _toggleExpansionInList(
    List<MoveFolderViewData> folders,
    String folderId,
  ) {
    return folders.map((folder) {
      if (folder.backendId == folderId) {
        return folder.copyWith(isExpanded: !folder.isExpanded);
      } else {
        return folder.copyWith(
          subfolders: _toggleExpansionInList(folder.subfolders, folderId),
        );
      }
    }).toList();
  }

  /// Ensure parent folder is expanded when creating a subfolder
  List<MoveFolderViewData> _ensureParentExpanded(
    List<MoveFolderViewData> folders,
    String parentFolderId,
  ) {
    return folders.map((folder) {
      if (folder.backendId == parentFolderId) {
        return folder.copyWith(isExpanded: true);
      } else {
        return folder.copyWith(
          subfolders: _ensureParentExpanded(folder.subfolders, parentFolderId),
        );
      }
    }).toList();
  }

  /// Check if target folder is a descendant of the source folder
  bool _isTargetDescendantOfFolder(
      String sourceFolderId, String targetFolderId) {
    final allFolders = HiveFolderService.getAllFolders();
    final folderMap = _createFolderMap(allFolders);

    final targetFolder = folderMap[targetFolderId];
    if (targetFolder == null) return false;

    String? currentParentId = targetFolder.parentFolderId;
    while (currentParentId != null && currentParentId.isNotEmpty) {
      if (currentParentId == sourceFolderId) return true;

      final parentFolder = folderMap[currentParentId];
      currentParentId = parentFolder?.parentFolderId;
    }

    return false;
  }

  static bool isInvalidTarget(
      FolderModel targetFolder, List<String>? foldersToBeMovedIds) {
    if (foldersToBeMovedIds == null || foldersToBeMovedIds.isEmpty) {
      return false;
    }

    if (foldersToBeMovedIds.contains(targetFolder.backendId)) {
      return true;
    }

    return foldersToBeMovedIds
        .any((folderId) => _isDescendantOf(targetFolder.backendId, folderId));
  }

  /// Checks if a folder is a descendant of another folder
  static bool _isDescendantOf(String potentialDescendant, String ancestorId) {
    final allFolders = HiveFolderService.getAllFolders();
    final folderMap = {for (var folder in allFolders) folder.backendId: folder};

    final folder = folderMap[potentialDescendant];
    if (folder == null || folder.parentFolderId == null) return false;
    if (folder.parentFolderId == ancestorId) return true;

    return _isDescendantOf(folder.parentFolderId!, ancestorId);
  }
}
