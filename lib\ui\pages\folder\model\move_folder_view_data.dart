import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:note_x/lib.dart';

import '../../../../data/local/model/folder/folder_model.dart';

part 'move_folder_view_data.freezed.dart';

@freezed
class MoveFolderViewData with _$MoveFolderViewData {
  const factory MoveFolderViewData({
    required String id,
    required String backendId,
    required String folderName,
    required String icon,
    required String createdAt,
    required String updatedAt,
    String? parentFolderId,
    @Default([]) List<MoveFolderViewData> subfolders,
    required String path,
    required int level,
    required int noteCount,
    @Default(false) bool isSelected,
    @Default(false) bool isExpanded,
    @Default(false) bool isInvalid,
  }) = _MoveFolderViewModel;

  // Factory constructor to create from FolderModel
  factory MoveFolderViewData.fromFolderModel(
    FolderModel folderModel, {
    bool isSelected = false,
    bool isExpanded = false,
    String jumpToFolderId = '',
  }) {
    return MoveFolderViewData(
      id: folderModel.id,
      backendId: folderModel.backendId,
      folderName: folderModel.folderName,
      icon: folderModel.icon,
      createdAt: folderModel.createdAt,
      updatedAt: folderModel.updatedAt,
      parentFolderId: folderModel.parentFolderId,
      subfolders: folderModel.subfolders.map((folder) => MoveFolderViewData.fromFolderModel(folder)).toList(),
      path: folderModel.path,
      level: folderModel.level,
      noteCount: folderModel.noteCount,
      isSelected: isSelected,
      isExpanded: isExpanded,
      isInvalid: MoveFolderCubit.isInvalidTarget(
         folderModel, [jumpToFolderId],
      ),
    );
  }
}
